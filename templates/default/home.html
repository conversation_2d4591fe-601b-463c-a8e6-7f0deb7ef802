<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ .title }}</title>
    <link rel="stylesheet" href="/assets/css/style.css">
</head>
<body>
    <div class="container">
        <!-- 导航栏 -->
        <nav class="navbar">
            <div class="nav-container">
                <div class="nav-brand">
                    <a href="/">视频网站</a>
                </div>
                <ul class="nav-menu">
                    {{ range .parentTypes }}
                    <li class="nav-item">
                        <a href="/list/{{ .TypeID }}" class="nav-link">{{ .TypeName }}</a>
                    </li>
                    {{ end }}
                </ul>
                <div class="nav-search">
                    <form action="/search" method="GET" class="search-form">
                        <input type="text" name="keyword" placeholder="搜索视频..." class="search-input">
                        <button type="submit" class="search-btn">搜索</button>
                    </form>
                </div>
            </div>
        </nav>

        <!-- 主要内容 -->
        <main class="main-content">
            <div class="content-wrapper">
                <!-- 轮播图区域 -->
                <section class="banner">
                    <div class="banner-content">
                        <h1>欢迎来到视频网站</h1>
                        <p>精彩视频，尽在这里</p>
                    </div>
                </section>

                <!-- 栏目视频展示 -->
                {{ range .parentTypes }}
                <section class="type-section">
                    <div class="section-header">
                        <h2 class="section-title">{{ .TypeName }}</h2>
                        <a href="/list/{{ .TypeID }}" class="more-link">更多 &raquo;</a>
                    </div>

                    <!-- 标签页导航 -->
                    <div class="tabs">
                        <div class="tab-nav">
                            <button class="tab-nav-item active" onclick="showTab('type-{{ .TypeID }}', 'latest', this)">最近发布</button>
                            <button class="tab-nav-item" onclick="showTab('type-{{ .TypeID }}', 'hot', this)">点击排行</button>
                            <button class="tab-nav-item" onclick="showTab('type-{{ .TypeID }}', 'random', this)">随机视频</button>
                        </div>

                        {{ $typeData := index $.typeVideos .TypeID }}{{ if $typeData }}
                        {{ $tabs := slice "latest" "hot" "random" }}{{ $limits := slice 10 20 15 }}{{ $names := slice "最近发布" "点击排行" "随机视频" }}{{ $actives := slice "active" "" "" }}
                        {{ range $i, $tabType := $tabs }}
                        <!-- {{ index $names $i }}标签页 -->
                        <div id="type-{{ $.TypeID }}-{{ $tabType }}" class="tab-content {{ index $actives $i }}">
                            <div class="video-grid">
                                {{ range $index, $video := (index $typeData $tabType) }}{{ if lt $index (index $limits $i) }}
                                <div class="video-item">
                                    <div class="video-thumb">
                                        <a href="/detail/{{ $video.VodID }}">
                                            {{ if $video.VodPic }}
                                            <img src="{{ fixImagePath $video.VodPic }}" alt="{{ $video.VodName }}" class="thumb-img">
                                            {{ else }}
                                            <div class="thumb-placeholder">暂无图片</div>
                                            {{ end }}
                                            <div class="video-info">
                                                <div class="video-title">{{ $video.VodName }}</div>
                                                <div class="video-meta">
                                                    <span class="video-year">{{ $video.VodYear }}</span>
                                                    {{ if $video.VodRemarks }}
                                                    <span class="video-remarks">{{ $video.VodRemarks }}</span>
                                                    {{ end }}
                                                </div>
                                            </div>
                                        </a>
                                    </div>
                                </div>
                                {{ end }}{{ end }}
                            </div>
                        </div>
                        {{ end }}{{ end }}
                    </div>
                </section>
                {{ end }}
            </div>
        </main>
    </div>

    <!-- 底部 -->
    <footer class="footer">
        <div class="footer-content">
            <p>&copy; 2024 视频网站. All rights reserved.</p>
        </div>
    </footer>

    <!-- JavaScript for tabs -->
    <script>
    function showTab(typeId, tabName, element) {
        // 找到当前栏目的tabs容器
        var tabsContainer = element.closest('.tabs');

        // 隐藏当前栏目的所有标签页内容
        var tabContents = tabsContainer.querySelectorAll('.tab-content');
        tabContents.forEach(function(content) {
            content.classList.remove('active');
        });

        // 移除当前栏目所有导航项的active类
        var tabNavItems = tabsContainer.querySelectorAll('.tab-nav-item');
        tabNavItems.forEach(function(item) {
            item.classList.remove('active');
        });

        // 显示选中的标签页内容
        document.getElementById(typeId + '-' + tabName).classList.add('active');

        // 添加选中导航项的active类
        element.classList.add('active');
    }
    </script>
</body>
</html>
